import UIKit
import SnapKit
import Then
import Combine
import CombineCocoa

/// 集结搜索页面控制器
class AssemblySearchController: BaseViewController {
    
    // MARK: - Properties
    
    /// 传入的笔记类型
    var noteType: NoteType = .all
    
    /// 搜索ViewModel
    private let viewModel = AssemblySearchViewModel()
    
    // MARK: - UI Components
    
    /// 搜索容器视图
    private lazy var searchContainerView = UIView().then {
        $0.backgroundColor = color_F6F8F9
        $0.layer.cornerRadius = 18
        $0.layer.masksToBounds = true
    }

    /// 搜索图标
    private lazy var searchIconImageView = UIImageView().then {
        $0.image = UIImage(systemName: "magnifyingglass")
        $0.tintColor = color_999999
        $0.contentMode = .scaleAspectFit
    }

    /// 搜索输入框
    private lazy var searchTextField = UITextField().then {
        $0.placeholder = "搜索内容"
        $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.textColor = color_3D3E40
        $0.backgroundColor = .clear
        $0.borderStyle = .none
        $0.becomeFirstResponder()
    }

    /// 清除按钮
    private lazy var clearButton = UIButton(type: .system).then {
        $0.setImage(UIImage(systemName: "xmark.circle.fill"), for: .normal)
        $0.tintColor = color_999999
        $0.isHidden = true
    }
    
    /// 搜索按钮
    private lazy var searchButton = UIButton(type: .system).then {
        $0.setTitle("搜索", for: .normal)
        $0.setTitleColor(.white, for: .normal)
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 12, weight: .medium)
        $0.backgroundColor = color_blue
        $0.layer.cornerRadius = 14
        $0.layer.masksToBounds = true
    }
    
    /// 搜索历史视图
    private lazy var searchHistoryView = SearchHistoryView().then {
        $0.delegate = self
        $0.isHidden = true
    }
    
    /// 表格视图
    private lazy var tableView = UITableView().then {
        $0.delegate = self
        $0.dataSource = self
        $0.backgroundColor = .clear
        $0.separatorStyle = .none
        $0.register(AssemblyNoteCell.self, forCellReuseIdentifier: AssemblyNoteCell.identifier)
        $0.isHidden = true // 初始隐藏
    }
    
    /// 搜索历史数据
    private var searchHistory: [String] = []

    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupNavigationBar()
        configUI()
        configLayout()
        setupBindings()
        loadSearchHistory()
        // 设置传入的笔记类型
        viewModel.noteType = noteType
    }
    

    
    // MARK: - UI Configuration
    
    /// 设置导航栏
    private func setupNavigationBar() {
        // 创建搜索容器并设置布局
        setupSearchContainer()

      
        // 设置包装容器作为titleView
        navigationItem.titleView = searchContainerView

        // 设置搜索按钮作为右侧按钮
        navigationItem.rightBarButtonItem = UIBarButtonItem(customView: searchButton)

        // 设置返回按钮
        navigationItem.hidesBackButton = false
    }

    /// 设置搜索容器
    private func setupSearchContainer() {
        // 添加子视图到搜索容器
        searchContainerView.addSubview(searchIconImageView)
        searchContainerView.addSubview(searchTextField)
        searchContainerView.addSubview(clearButton)

        searchContainerView.snp.makeConstraints { make in
            make.height.equalTo(36)
            make.width.equalTo(250)
        }
        // 搜索图标约束
        searchIconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }

        // 搜索输入框约束
        searchTextField.snp.makeConstraints { make in
            make.left.equalTo(searchIconImageView.snp.right).offset(8)
            make.centerY.equalToSuperview()
            make.right.equalTo(clearButton.snp.left).offset(-8)
        }

        // 清除按钮约束
        clearButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
        }
    }
    
    /// UI配置
    override func configUI() {

        // 添加子视图
        view.addSubview(searchHistoryView)
        view.addSubview(tableView)
    }
    
    /// UI布局
    override func configLayout() {
        // 搜索按钮尺寸约束
        searchButton.snp.makeConstraints { make in
            make.width.equalTo(58)
            make.height.equalTo(28)
        }

        // 搜索历史视图
        searchHistoryView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(24)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        // 表格视图
        tableView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(16)
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    /// 设置事件绑定
    override func setupBindings() {
        // 使用BaseViewController的便捷方法设置刷新
        setupRefresh(for: tableView, with: viewModel)
        
        // 搜索按钮点击事件
        searchButton.tapPublisher
            .sink { [weak self] _ in
                self?.performSearch()
            }
            .store(in: &cancellables)
        
        // 清除按钮点击事件
        clearButton.tapPublisher
            .sink { [weak self] _ in
                self?.searchTextField.text = ""
                self?.clearButton.isHidden = true
            }
            .store(in: &cancellables)
        
        // 搜索框文本变化事件
        searchTextField.textPublisher
            .compactMap { $0 }
            .sink { [weak self] (text: String) in
                // 控制清除按钮的显示/隐藏
                self?.clearButton.isHidden = text.isEmpty
            }
            .store(in: &cancellables)

        // 监听搜索框编辑状态
        NotificationCenter.default.publisher(for: UITextField.textDidBeginEditingNotification, object: searchTextField)
            .sink { [weak self] _ in
                // 开始编辑时显示历史搜索
                self?.showSearchHistoryView()
            }
            .store(in: &cancellables)

        NotificationCenter.default.publisher(for: UITextField.textDidEndEditingNotification, object: searchTextField)
            .sink { [weak self] _ in
                // 结束编辑时显示搜索结果列表
                self?.showSearchResultsView()
            }
            .store(in: &cancellables)

        // 监听搜索结果
        viewModel.$notes
            .receive(on: DispatchQueue.main)
            .sink { [weak self] notes in
                self?.tableView.reloadData()
                self?.updateUIForSearchResults(hasResults: !notes.isEmpty)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Private Methods
    
    /// 执行搜索
    private func performSearch() {
        guard let keyword = searchTextField.text?.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines),
              !keyword.isEmpty else {
            return
        }

        // 保存搜索历史
        saveSearchKeyword(keyword)

        // 执行搜索
        viewModel.searchKeyword = keyword

        // 隐藏键盘，结束编辑状态
        searchTextField.resignFirstResponder()

        // 执行搜索请求
        viewModel.refreshData()

        // 显示搜索结果界面
        showSearchResultsView()
    }
    
    /// 更新UI显示搜索结果
    private func updateUIForSearchResults(hasResults: Bool) {
        // 搜索结果更新时不改变当前显示状态，由编辑状态控制
    }

    /// 显示搜索历史视图
    private func showSearchHistoryView() {
        searchHistoryView.isHidden = false
        tableView.isHidden = true

        // 完全禁用tableView的交互，防止下拉刷新
        tableView.isUserInteractionEnabled = false
        tableView.isScrollEnabled = false

        // 禁用刷新控件
        if let refreshControl = tableView.refreshControl {
            refreshControl.endRefreshing()
            refreshControl.removeFromSuperview()
        }

    }

    /// 显示搜索结果视图
    private func showSearchResultsView() {
        searchHistoryView.isHidden = true
        tableView.isHidden = false
    }



    /// 加载搜索历史
    private func loadSearchHistory() {
        let history = UserDefaults.standard.stringArray(forKey: "AssemblySearchHistory") ?? []
        searchHistoryView.updateSearchHistory(history)
    }
    
    /// 保存搜索关键词
    private func saveSearchKeyword(_ keyword: String) {
        var history = UserDefaults.standard.stringArray(forKey: "AssemblySearchHistory") ?? []
        // 移除重复项
        history.removeAll { $0 == keyword }
        // 添加到开头
        history.insert(keyword, at: 0)
        // 限制历史记录数量
        if history.count > 10 {
            history = Array(history.prefix(10))
        }
        // 保存到UserDefaults
        UserDefaults.standard.set(history, forKey: "AssemblySearchHistory")
        // 更新搜索历史视图
        searchHistoryView.updateSearchHistory(history)
    }

}

// MARK: - SearchHistoryViewDelegate

extension AssemblySearchController: SearchHistoryViewDelegate {
    func searchHistoryView(_ view: SearchHistoryView, didSelectItem keyword: String) {
        // 设置搜索关键词并直接执行搜索
        searchTextField.text = keyword
        viewModel.searchKeyword = keyword

        // 隐藏键盘，结束编辑状态
        searchTextField.resignFirstResponder()

        // 执行搜索
        viewModel.refreshData()

        // 显示搜索结果界面
        showSearchResultsView()
    }

    func searchHistoryView(_ view: SearchHistoryView, didDeleteItem keyword: String) {
        // 从UserDefaults中删除该项
        var history = UserDefaults.standard.stringArray(forKey: "AssemblySearchHistory") ?? []
        history.removeAll { $0 == keyword }
        UserDefaults.standard.set(history, forKey: "AssemblySearchHistory")
    }
}

// MARK: - UITableViewDataSource

extension AssemblySearchController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel.notes.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: AssemblyNoteCell.identifier, for: indexPath) as! AssemblyNoteCell
        let note = viewModel.notes[indexPath.row]
        cell.configure(with: note)
        cell.delegate = self
        return cell
    }
}

// MARK: - UITableViewDelegate

extension AssemblySearchController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        let note = viewModel.notes[indexPath.row]
        // TODO: 跳转到详情页面
        print("点击了搜索结果: \(note.title)")
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath) -> CGFloat {
        return 200
    }
}

// MARK: - UIGestureRecognizerDelegate

extension AssemblySearchController: UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        // 当显示搜索历史时，拦截手势优先级最高
        return false
    }
}

// MARK: - AssemblyNoteCellDelegate

extension AssemblySearchController: AssemblyNoteCellDelegate {
    func assemblyNoteCellDidTapLike(_ cell: AssemblyNoteCell) {
        guard let indexPath = tableView.indexPath(for: cell) else { return }
        let note = viewModel.notes[indexPath.row]
        // TODO: 处理点赞逻辑
        print("点赞了搜索结果: \(note.title)")
    }

    func assemblyNoteCellDidTapComment(_ cell: AssemblyNoteCell) {
        guard let indexPath = tableView.indexPath(for: cell) else { return }
        let note = viewModel.notes[indexPath.row]
        // TODO: 处理评论逻辑
        print("评论了搜索结果: \(note.title)")
    }
}
