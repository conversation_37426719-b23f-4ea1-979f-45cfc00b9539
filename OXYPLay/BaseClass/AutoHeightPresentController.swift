//
//  AutoHeightPresentController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/1.
//

import UIKit
import SnapKit
import Then

/// 自适应高度的弹出控制器示例
class AutoHeightPresentController: BasePresentController {
    
    // MARK: - Properties
    
    /// 内容标签数组
    private var contentLabels: [UILabel] = []
    
    // MARK: - 重写presentationHeight返回nil启用自适应
    override var presentationHeight: CGFloat? {
        return nil // 返回nil启用自适应高度
    }
    
    // MARK: - UI Components
    
    private lazy var stackView = UIStackView().then {
        $0.axis = .vertical
        $0.spacing = 16
        $0.alignment = .fill
        $0.distribution = .fill
    }
    
    private lazy var addContentButton = BaseButton().then {
        $0.setTitle("添加内容", for: .normal)
        $0.setTitleColor(.white, for: .normal)
        $0.backgroundColor = color_blue
        $0.isRounded = true
    }
    
    private lazy var removeContentButton = BaseButton().then {
        $0.setTitle("移除内容", for: .normal)
        $0.setTitleColor(.white, for: .normal)
        $0.backgroundColor = UIColor.systemRed
        $0.isRounded = true
    }
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupInitialContent()
    }
    
    // MARK: - UI Setup
    
    override func configUI() {
        super.configUI()
        configView(title: "自适应高度示例", bottomTitle: "完成")
        
        // 添加滚动视图以支持内容超出屏幕的情况
        let scrollView = UIScrollView()
        contentView.addSubview(scrollView)
        scrollView.addSubview(stackView)
        
        // 添加按钮容器
        let buttonContainer = UIView()
        buttonContainer.addSubview(addContentButton)
        buttonContainer.addSubview(removeContentButton)
        
        stackView.addArrangedSubview(buttonContainer)
        
        // 设置约束
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
            make.width.equalTo(scrollView).offset(-32)
        }
        
        addContentButton.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.height.equalTo(44)
        }
        
        removeContentButton.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.left.equalTo(addContentButton.snp.right).offset(12)
            make.width.equalTo(addContentButton)
            make.height.equalTo(44)
        }
        
        buttonContainer.snp.makeConstraints { make in
            make.height.equalTo(44)
        }
    }
    
    // MARK: - Setup Bindings
    
    override func setupBindings() {
        super.setupBindings()
        
        // 添加内容按钮
        addContentButton.tapPublisher
            .sink { [weak self] _ in
                self?.addContent()
            }
            .store(in: &cancellables)
        
        // 移除内容按钮
        removeContentButton.tapPublisher
            .sink { [weak self] _ in
                self?.removeContent()
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Private Methods
    
    /// 设置初始内容
    private func setupInitialContent() {
        addContent()
        addContent()
    }
    
    /// 添加内容
    private func addContent() {
        let label = UILabel().then {
            $0.text = "这是第\(contentLabels.count + 1)行内容，用于演示自适应高度功能。当内容增加时，弹窗高度会自动调整。"
            $0.font = UIFont.systemFont(ofSize: 14)
            $0.textColor = color_2B2C2F
            $0.numberOfLines = 0
            $0.backgroundColor = color_F6F8F9
            $0.layer.cornerRadius = 8
            $0.layer.masksToBounds = true
            $0.textAlignment = .center
        }
        
        // 添加内边距
        label.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(60)
        }
        
        contentLabels.append(label)
        
        // 插入到按钮容器之前
        let insertIndex = max(0, stackView.arrangedSubviews.count - 1)
        stackView.insertArrangedSubview(label, at: insertIndex)
        
        // 更新弹窗高度
        updatePresentationHeight()
        
        // 添加动画效果
        label.alpha = 0
        UIView.animate(withDuration: 0.3) {
            label.alpha = 1
        }
    }
    
    /// 移除内容
    private func removeContent() {
        guard !contentLabels.isEmpty else { return }
        
        let labelToRemove = contentLabels.removeLast()
        
        // 添加动画效果
        UIView.animate(withDuration: 0.3, animations: {
            labelToRemove.alpha = 0
        }) { _ in
            self.stackView.removeArrangedSubview(labelToRemove)
            labelToRemove.removeFromSuperview()
            
            // 更新弹窗高度
            self.updatePresentationHeight()
        }
    }
}
