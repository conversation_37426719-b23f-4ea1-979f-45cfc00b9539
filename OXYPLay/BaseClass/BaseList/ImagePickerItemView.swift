//
//  ImagePickerItemView.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/15.
//

import UIKit
import Combine
import CombineCocoa
import ZLPhotoBrowser

// MARK: - 图片选择列表项

class ImagePickerItemView: ListItemView {
    
    // MARK: - 属性
    
    private let maxImageCount = 9
    private var selectedImages: [UIImage] = []
    var addImage = UIImage(named: "baselist_addimage")
    // MARK: - UI组件
    
    lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 12
        layout.itemSize = CGSize(width: 80, height: 80)
        layout.sectionInset = UIEdgeInsets(top: 0, left: 12, bottom: 0, right: 12)
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.register(AddImageCell.self, forCellWithReuseIdentifier: "AddImageCell")
        collectionView.register(PublishImageCell.self, forCellWithReuseIdentifier: "PublishImageCell")
        collectionView.dataSource = self
        collectionView.delegate = self
        return collectionView
    }()
    
  
    
    // MARK: - 初始化方法
    
    override func configUI() {
        backgroundColor = .white
        addSubview(collectionView)
    }
    
    override func configLayout() {
        collectionView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(15)
            make.bottom.equalTo(-15)
            make.height.equalTo(80)
        }
    }
    
    // MARK: - 配置方法
    
    override func configure(with config: ListItemConfig) {
        super.configure(with: config)
        
        if let images = config.data as? [UIImage] {
            selectedImages = images
            data = images
            collectionView.reloadData()
        }
    }
    
    // MARK: - 公共方法
    
    override func getData() -> Any? {
        return selectedImages
    }
    
    // MARK: - 私有方法
    
    private func showImagePicker() {
        guard let vc = UIViewController.getCurrentViewController() else {
            return
        }
        
        // 检查是否已达到最大图片数量
        if selectedImages.count >= maxImageCount {
            // 可以添加提示，告知用户已达到最大图片数量
            return
        }
        
        let pickerConfig = ZLPhotoConfiguration.default()
        pickerConfig.maxSelectCount = maxImageCount - selectedImages.count
        pickerConfig.maxSelectVideoDuration = 5
        let ps = ZLPhotoPreviewSheet()
        
        ps.selectImageBlock = { [weak self] results, isOriginal in
            guard let self = self else { return }
            
            // 确保添加后不超过最大数量
            let availableSlots = self.maxImageCount - self.selectedImages.count
            let imagesToAdd = Array(results.prefix(availableSlots))
            
            for result in imagesToAdd {
                self.selectedImages.append(result.image)
            }
            self.data = self.selectedImages
            self.collectionView.reloadData()
        }
        ps.showPhotoLibrary(sender: vc)
    }
}

// MARK: - UICollectionViewDataSource, UICollectionViewDelegate

extension ImagePickerItemView: UICollectionViewDataSource, UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        // 如果已经选择了最大数量的图片，只显示已选图片，不显示添加按钮
        if selectedImages.count >= maxImageCount {
            return maxImageCount
        }
        // 否则显示已选图片 + 添加按钮
        return selectedImages.count + 1
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if indexPath.item == selectedImages.count && selectedImages.count < maxImageCount {
            // 添加按钮
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "AddImageCell", for: indexPath) as! AddImageCell
            cell.plusImageView.image = addImage
            return cell
        } else {
            // 图片单元格
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "PublishImageCell", for: indexPath) as! PublishImageCell
            // 确保索引在数组范围内
            if indexPath.item < selectedImages.count {
                cell.imageView.image = selectedImages[indexPath.item]
            }
            cell.delegate = self
            cell.tag = indexPath.item
            return cell
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if indexPath.item == selectedImages.count && selectedImages.count < maxImageCount {
            // 点击添加按钮
            showImagePicker()
        }
    }
}

// MARK: - ImageCellDelegate

extension ImagePickerItemView: PublishImageCellDelegate {
    func didTapDeleteButton(in cell: PublishImageCell, button: UIButton) {
        let index = cell.tag
        guard index < selectedImages.count else { return }
        
        // 删除图片
        selectedImages.remove(at: index)
        data = selectedImages
        collectionView.reloadData()
    }
}

// MARK: - 图片单元格

protocol PublishImageCellDelegate: AnyObject {
    func didTapDeleteButton(in cell: PublishImageCell, button: UIButton)
}

class PublishImageCell: UICollectionViewCell {
    
    weak var delegate: PublishImageCellDelegate?
    
    lazy var imageView = UIImageView().then {
        $0.contentMode = .scaleToFill
        $0.clipsToBounds = true
        $0.layer.cornerRadius = 16
    }
    
    lazy var deleteButton = UIButton().then {
        $0.setImage(UIImage(named: "baselist_deleteimage"), for: .normal)
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    private func setupUI() {
        contentView.addSubview(imageView)
        contentView.addSubview(deleteButton)
        
        imageView.snp.makeConstraints { make in
            make.left.top.right.bottom.equalToSuperview()
        }
        
        deleteButton.snp.makeConstraints { make in
            make.centerX.equalTo(imageView.snp.right)
            make.centerY.equalTo(imageView.snp.top)
            make.width.height.equalTo(22)
        }
        
        deleteButton.addTarget(self, action: #selector(deleteButtonTapped), for: .touchUpInside)
    }
    
    @objc private func deleteButtonTapped(_ sender: UIButton) {
        delegate?.didTapDeleteButton(in: self, button: sender)
    }
}

// MARK: - 添加图片单元格

class AddImageCell: UICollectionViewCell {
    
    lazy var plusImageView = UIImageView().then {
        $0.image = UIImage(named: "baselist_addimage")
        $0.layer.cornerRadius = 16
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    private func setupUI() {
        contentView.addSubview(plusImageView)
        
        plusImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
    }
}


