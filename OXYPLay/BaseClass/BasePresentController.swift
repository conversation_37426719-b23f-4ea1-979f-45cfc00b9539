import UIKit

class BasePresentController: BaseViewController, CustomPresentable {
    var presentationDirection: PresentationDirection{
        return .bottomToTop
    }
    
    var rightMargin: CGFloat = 0.0
    
    
    // 实现CustomPresentable协议
    var presentationHeight: CGFloat? {
        return nil // 屏幕高度的75%，子类可以重写返回nil来自适应
    }
    lazy var titleLabel = UILabel().then {
        $0.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.textColor = color_2B2C2F
    }
    
    private lazy var closeButton = UIButton().then {
        $0.setImage(UIImage(named: "closebuttonicon"), for: .normal)
    }
    lazy var contentView = UIView()
    lazy var bottomView = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
        $0.layer.maskedCorners = [.layerMinXMinYCorner,.layerMaxXMinYCorner]
        $0.masksToBounds = true
    }
    lazy var bottomButton = BaseGradientButton().then {
        $0.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        $0.setTitleColor(.white, for: .normal)
        $0.backgroundColor = color_blue
        $0.isRounded = true
    }
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupPresentationObservers()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 视图即将出现时，检查是否需要重新计算高度
        if presentationHeight == nil {
            DispatchQueue.main.async { [weak self] in
                self?.updatePresentationHeight()
            }
        }
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        // 视图出现时，确保高度正确
        recalculateHeightIfNeeded()
    }
    
    // MARK: - UI设置
    
    private func setupUI() {
        view.backgroundColor = color_F6F8F9
        
        // 添加头部视图
        view.addSubview(closeButton)
        view.addSubview(titleLabel)
        view.addSubview(contentView)
        view.addSubview(bottomView)
        bottomView.addSubview(bottomButton)
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(14)
            make.right.equalTo(-14)
        }
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(30)
            make.centerX.equalToSuperview()
        }
        contentView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(24)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(bottomView.snp.top).offset(-12)
        }
        bottomView.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(ScreenInfo.totalTabBarHeight)
        }
        bottomButton.snp.makeConstraints { make in
            make.left.top.equalTo(12)
            make.right.equalTo(-12)
            make.height.equalTo(44)
        }
        closeButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                self.dismiss(animated: true)
            }
            .store(in: &cancellables)
    }
    func hideBottomBar(){
        bottomView.isHidden = true
        contentView.snp.updateConstraints { make in
            make.bottom.equalTo(bottomView.snp.top).offset(ScreenInfo.totalTabBarHeight)
        }
    }
    func configView(title:String?, bottomTitle:String = "确定"){
        self.bottomButton.setTitle(bottomTitle, for: .normal)
        self.titleLabel.text = title
    }

    /// 启用自适应高度（子类调用此方法来启用自适应高度）
    func enableAutoHeight() {
        // 子类可以重写presentationHeight返回nil，或者调用此方法
    }

    /// 当内容发生变化时，更新弹窗高度（仅在自适应模式下有效）
    func updatePresentationHeight() {
        guard presentationHeight == nil else { return }

        // 强制布局更新
        view.setNeedsLayout()
        view.layoutIfNeeded()

        // 通知presentation controller重新计算高度
        if let customPresentationController = presentationController as? CustomPresentationController {
            customPresentationController.recalculateHeight()
        }
    }

    // MARK: - Private Methods

    /// 设置present/dismiss监听
    private func setupPresentationObservers() {
        // 监听应用状态变化，当从后台回到前台时重新计算高度
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(applicationDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
    }

    /// 应用变为活跃状态
    @objc private func applicationDidBecomeActive() {
        // 当应用重新变为活跃状态时，重新计算高度
        recalculateHeightIfNeeded()
    }

    // 重写present方法，在completion中添加监听
    override func present(_ viewControllerToPresent: UIViewController, animated: Bool, completion: (() -> Void)?) {
        super.present(viewControllerToPresent, animated: animated) { [weak self] in
            completion?()
            // present完成后，开始监听dismiss
            self?.monitorDismiss(of: viewControllerToPresent)
        }
    }

    /// 监听控制器的dismiss
    private func monitorDismiss(of viewController: UIViewController) {
        // 使用定时器定期检查presentedViewController是否还存在
        let timer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: true) { [weak self, weak viewController] timer in
            // 如果viewController被释放或者不再是presentedViewController，说明已经dismiss
            if viewController == nil || self?.presentedViewController != viewController {
                timer.invalidate()
                // 延迟一点时间再重新计算高度，确保dismiss动画完成
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    self?.recalculateHeightIfNeeded()
                }
            }
        }

        // 将timer添加到RunLoop中
        RunLoop.main.add(timer, forMode: .common)
    }

    /// 重新计算高度（如果需要）
    private func recalculateHeightIfNeeded() {
        guard presentationHeight == nil else { return }

        // 延迟一点时间，确保系统布局完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            self?.updatePresentationHeight()
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}
