import UIKit

// 用于设置弹出视图高度的协议
protocol CustomPresentable {
    var presentationHeight: CGFloat? { get } // 改为可选，nil时自适应高度
    var presentationDirection: PresentationDirection { get }
    var rightMargin: CGFloat { get }
}

// 弹出方向枚举
enum PresentationDirection {
    case bottomToTop // 从底部向上弹出
    case leftToRight // 从左向右弹出
}

// 自定义转场动画控制器
class CustomPresentationController: UIPresentationController {
    
    // 蒙版视图
    private lazy var dimmingView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.4)
        view.alpha = 0.0
        
        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        view.addGestureRecognizer(tapGesture)
        
        return view
    }()
    
    // 处理点击蒙版事件
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        presentedViewController.dismiss(animated: true)
    }
    
    // 在呈现转换即将开始时被调用
    override func presentationTransitionWillBegin() {
        guard let containerView = containerView else { return }
        
        // 添加并设置蒙版视图
        dimmingView.frame = containerView.bounds
        containerView.insertSubview(dimmingView, at: 0)
        
        // 动画显示蒙版
        if let coordinator = presentedViewController.transitionCoordinator {
            coordinator.animate(alongsideTransition: { [weak self] _ in
                self?.dimmingView.alpha = 1.0
            })
        } else {
            dimmingView.alpha = 1.0
        }
    }
    
    // 在呈现转换结束时被调用
    override func presentationTransitionDidEnd(_ completed: Bool) {
        if !completed {
            dimmingView.removeFromSuperview()
        }
    }
    
    // 在解除呈现转换即将开始时被调用
    override func dismissalTransitionWillBegin() {
        if let coordinator = presentedViewController.transitionCoordinator {
            coordinator.animate(alongsideTransition: { [weak self] _ in
                self?.dimmingView.alpha = 0.0
            })
        } else {
            dimmingView.alpha = 0.0
        }
    }
    
    // 在解除呈现转换结束时被调用
    override func dismissalTransitionDidEnd(_ completed: Bool) {
        if completed {
            dimmingView.removeFromSuperview()
        }
    }
    
    // 定义呈现视图的尺寸
    override var frameOfPresentedViewInContainerView: CGRect {
        guard let containerView = containerView else { return .zero }

        let direction: PresentationDirection
        let height: CGFloat
        let rightMargin: CGFloat

        // 如果遵循了CustomPresentable协议，使用协议中定义的参数
        if let presentable = presentedViewController as? CustomPresentable {
            direction = presentable.presentationDirection
            rightMargin = presentable.rightMargin

            // 如果presentationHeight为nil，则自适应高度
            if let customHeight = presentable.presentationHeight {
                height = customHeight
            } else {
                height = calculateAutoHeight(for: presentedViewController, in: containerView)
            }
        } else {
            // 默认值
            direction = .bottomToTop
            height = containerView.bounds.height / 2
            rightMargin = 0
        }

        switch direction {
        case .bottomToTop:
            return CGRect(
                x: 0,
                y: containerView.bounds.height - height,
                width: containerView.bounds.width,
                height: height
            )
        case .leftToRight:
            let width = containerView.bounds.width - rightMargin
            return CGRect(
                x: 0,
                y: 0,
                width: width,
                height: containerView.bounds.height
            )
        }
    }

    // 计算自适应高度
    private func calculateAutoHeight(for viewController: UIViewController, in containerView: UIView) -> CGFloat {
        // 确保视图已经加载
        viewController.loadViewIfNeeded()

        // 设置视图的宽度约束，让其计算所需高度
        let targetWidth = containerView.bounds.width
        let targetSize = CGSize(width: targetWidth, height: UIView.layoutFittingCompressedSize.height)

        // 使用systemLayoutSizeFitting计算所需高度
        let fittingSize = viewController.view.systemLayoutSizeFitting(
            targetSize,
            withHorizontalFittingPriority: .required,
            verticalFittingPriority: .fittingSizeLevel
        )

        // 限制最大高度为屏幕高度的80%，最小高度为200
        let maxHeight = containerView.bounds.height * 0.8
        let minHeight: CGFloat = 200

        return max(minHeight, min(fittingSize.height, maxHeight))
    }
}

// 自定义转场动画
class CustomPresentationAnimator: NSObject, UIViewControllerAnimatedTransitioning {
    
    let isPresenting: Bool
    
    init(isPresenting: Bool) {
        self.isPresenting = isPresenting
        super.init()
    }
    
    func transitionDuration(using transitionContext: UIViewControllerContextTransitioning?) -> TimeInterval {
        return 0.3
    }
    
    func animateTransition(using transitionContext: UIViewControllerContextTransitioning) {
        if isPresenting {
            animatePresentation(using: transitionContext)
        } else {
            animateDismissal(using: transitionContext)
        }
    }
    
    private func animatePresentation(using transitionContext: UIViewControllerContextTransitioning) {
        guard let toView = transitionContext.view(forKey: .to),
              let toViewController = transitionContext.viewController(forKey: .to) else { return }
        
        let containerView = transitionContext.containerView
        containerView.addSubview(toView)
        
        // 确定动画方向
        let direction: PresentationDirection = (toViewController as? CustomPresentable)?.presentationDirection ?? .bottomToTop
        
        // 设置初始位置
        toView.frame = transitionContext.finalFrame(for: toViewController)
        
        switch direction {
        case .bottomToTop:
            toView.transform = CGAffineTransform(translationX: 0, y: toView.frame.height)
            
            // 添加圆角
            toView.layer.cornerRadius = 12
            toView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
            toView.clipsToBounds = true
            
        case .leftToRight:
            toView.transform = CGAffineTransform(translationX: -toView.frame.width, y: 0)
            
            // 添加圆角
            toView.layer.cornerRadius = 12
            toView.layer.maskedCorners = [.layerMaxXMinYCorner, .layerMaxXMaxYCorner]
            toView.clipsToBounds = true
        }
        
        // 执行动画
        let duration = transitionDuration(using: transitionContext)
        UIView.animate(withDuration: duration, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0.2, options: .curveEaseOut, animations: {
            toView.transform = .identity
        }, completion: { finished in
            transitionContext.completeTransition(!transitionContext.transitionWasCancelled)
        })
    }
    
    private func animateDismissal(using transitionContext: UIViewControllerContextTransitioning) {
        guard let fromView = transitionContext.view(forKey: .from),
              let fromViewController = transitionContext.viewController(forKey: .from) else { return }
        
        // 确定动画方向
        let direction: PresentationDirection = (fromViewController as? CustomPresentable)?.presentationDirection ?? .bottomToTop
        
        // 执行动画
        let duration = transitionDuration(using: transitionContext)
        UIView.animate(withDuration: duration, delay: 0, options: .curveEaseIn, animations: {
            switch direction {
            case .bottomToTop:
                fromView.transform = CGAffineTransform(translationX: 0, y: fromView.frame.height)
            case .leftToRight:
                fromView.transform = CGAffineTransform(translationX: -fromView.frame.width, y: 0)
            }
        }, completion: { finished in
            transitionContext.completeTransition(!transitionContext.transitionWasCancelled)
        })
    }
}

// 转场代理
class CustomPresentationManager: NSObject, UIViewControllerTransitioningDelegate {
    
    func presentationController(forPresented presented: UIViewController, presenting: UIViewController?, source: UIViewController) -> UIPresentationController? {
        return CustomPresentationController(presentedViewController: presented, presenting: presenting)
    }
    
    func animationController(forPresented presented: UIViewController, presenting: UIViewController, source: UIViewController) -> UIViewControllerAnimatedTransitioning? {
        return CustomPresentationAnimator(isPresenting: true)
    }
    
    func animationController(forDismissed dismissed: UIViewController) -> UIViewControllerAnimatedTransitioning? {
        return CustomPresentationAnimator(isPresenting: false)
    }
}

// 扩展UIViewController，添加便捷方法
extension UIViewController {
    
    // 自定义present方法
    func customPresent(_ viewControllerToPresent: UIViewController, animated: Bool, completion: (() -> Void)? = nil) {
        let presentationManager = CustomPresentationManager()
        viewControllerToPresent.modalPresentationStyle = .custom
        viewControllerToPresent.transitioningDelegate = presentationManager
        
        // 保存转场代理对象，防止被释放
        objc_setAssociatedObject(
            viewControllerToPresent,
            &AssociatedKeys.presentationManager,
            presentationManager,
            .OBJC_ASSOCIATION_RETAIN_NONATOMIC
        )
        
        present(viewControllerToPresent, animated: animated, completion: completion)
    }
}

// 关联对象的键
private struct AssociatedKeys {
    static var presentationManager = "presentationManager"
} 