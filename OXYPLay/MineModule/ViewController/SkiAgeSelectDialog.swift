//
//  SkiAgeSelectDialog.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/13.
//

import UIKit
import SnapKit
import Then
import Combine
import CombineCocoa
import SwiftDate

/// 雪龄选择弹窗
class SkiAgeSelectDialog: BasePresentController {
    
    // MARK: - Properties
    
    /// 选择完成回调
    let selectionCompletedPublisher = PassthroughSubject<(String, Bool), Never>()
    
    /// 当前选中的雪龄
    private var currentSkiAge: String = ""
    
    /// 是否公开显示
    private var isPublicDisplay: Bool = true
    
    // MARK: - UI Components
    
    /// 自定义日期选择器
    private lazy var datePicker: CustomDatePicker = {
        let config = CustomDatePickerConfig(
            yearRange: .past(years: 10),
            showYearSuffix: true,
            showMonthSuffix: true,
            needValidation: false
        )
        return CustomDatePicker(config: config)
    }()
    


    /// 公开显示容器
    private lazy var publicDisplayContainer = UIView().then {
        $0.backgroundColor = .white
        $0.layer.cornerRadius = 16
    }
    
    /// 公开显示标签
    private lazy var publicDisplayLabel = UILabel().then {
        $0.text = "是否公开展示"
        $0.font = UIFont.systemFont(ofSize: 12, weight: .regular)
        $0.textColor = color_2B2C2F80
    }
    
    /// 显示性别标签标签
    private lazy var showGenderLabelLabel = UILabel().then {
        $0.text = "展示雪龄标签"
        $0.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        $0.textColor = color_2B2C2F
    }
    
    
    /// 显示雪龄标签开关
    private lazy var showSkiAgeLabelSwitch = UISwitch().then {
        $0.onTintColor = color_blue
        $0.isOn = true
    }
    
    // MARK: - 初始化
    
    init(currentSkiAge: String = "", isPublicDisplay: Bool = true) {
        self.currentSkiAge = currentSkiAge
        self.isPublicDisplay = isPublicDisplay
        super.init()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    
    override var presentationHeight: CGFloat {
        return 487
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
        setupInitialDate()
    }
    
    // MARK: - UI Setup
    
    override func configUI() {
        configView(title: "首次滑雪时间", bottomTitle: "确认")
        datePicker.backgroundColor = .white
        datePicker.layer.cornerRadius = 16
        datePicker.masksToBounds = true
        // 添加自定义日期选择器
        contentView.addSubview(datePicker)

        // 添加公开显示容器
        contentView.addSubview(publicDisplayContainer)
        contentView.addSubview(publicDisplayLabel)
        publicDisplayContainer.addSubview(showGenderLabelLabel)
        publicDisplayContainer.addSubview(showSkiAgeLabelSwitch)
        setupConstraints()
    }
    
    private func setupConstraints() {
        // 自定义日期选择器约束
        datePicker.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(0)
            make.left.right.equalToSuperview().inset(12)
            make.height.equalTo(200)
        }
        publicDisplayLabel.snp.makeConstraints { make in
            make.top.equalTo(datePicker.snp.bottom).offset(12)
            make.left.equalTo(12)
        }
        // 公开显示容器约束
        publicDisplayContainer.snp.makeConstraints { make in
            make.top.equalTo(publicDisplayLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(12)
            make.height.equalTo(37)
        }
        
        // 公开显示标签约束
        showGenderLabelLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
        }
        
        // 显示性别标签开关约束
        showSkiAgeLabelSwitch.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
        }
      
    }
    
    // MARK: - Bindings
    
    override func setupBindings() {
        super.setupBindings()

        // 监听日期选择变化
        datePicker.dateSelectedPublisher
            .sink { [weak self] (year, month) in
                // 可以在这里处理日期变化
            }
            .store(in: &cancellables)

        // 确认按钮
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self else { return }
                let skiAge = self.datePicker.getSelectedDateString()
                self.selectionCompletedPublisher.send((skiAge, self.showSkiAgeLabelSwitch.isOn))
                self.dismiss(animated: true)
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Private Methods

    /// 设置初始日期
    private func setupInitialDate() {
        if !currentSkiAge.isEmpty {
            datePicker.setSelectedDate(dateString: currentSkiAge)
        } else {
            let now = Date()
            datePicker.setSelectedDate(year: now.year, month: now.month)
        }
    }
    
    // MARK: - Public Methods
    
    /// 配置雪龄选择弹窗
    /// - Parameters:
    ///   - currentSkiAge: 当前雪龄
    ///   - isPublicDisplay: 是否公开显示
    func configure(currentSkiAge: String, isPublicDisplay: Bool) {
        self.currentSkiAge = currentSkiAge
        self.isPublicDisplay = isPublicDisplay
        showSkiAgeLabelSwitch.isOn = isPublicDisplay
        setupInitialDate()
    }
}
