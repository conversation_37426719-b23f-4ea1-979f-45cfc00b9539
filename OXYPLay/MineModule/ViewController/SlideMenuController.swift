//  SlideMenuController.swift
//  OXYPLay
//
//  Created by Renhw on 2023/7/5.
//

import UIKit
import SnapKit
import Combine

protocol SlideMenuControllerDelegate: AnyObject {
    func slideMenuDidSelectItem(_ menuItem: SlideMenuController.MenuItem, title: String)
    func slideMenuDidTapLogout()
}

class SlideMenuController: UIViewController, CustomPresentable {
    
    // MARK: - Menu Item 枚举
    enum MenuItem: CaseIterable {
        case addFriend
        case myComments
        case browsingHistory
        case shoppingCart
        case purchased
        case sold
        case address
        case settings
        case customerService
        
        var identifier: String {
            switch self {
            case .addFriend: return "slide_addFriend"
            case .myComments: return "slide_myComments"
            case .browsingHistory: return "slide_browsingHistory"
            case .shoppingCart: return "slide_shoppingCart"
            case .purchased: return "slide_purchased"
            case .sold: return "slide_sold"
            case .address: return "slide_address"
            case .settings: return "slide_settings"
            case .customerService: return "slide_customerService"
            }
        }
        
        var title: String {
            switch self {
            case .addFriend: return "添加好友"
            case .myComments: return "我的评论"
            case .browsingHistory: return "历史浏览"
            case .shoppingCart: return "购物车"
            case .purchased: return "我买到的"
            case .sold: return "我卖出的"
            case .address: return "地址管理"
            case .settings: return "设置"
            case .customerService: return "客服帮助"
            }
        }
        
        var iconString: String {
            switch self {
            case .addFriend: return "slide_addFriend"
            case .myComments: return "slide_myComments"
            case .browsingHistory: return "slide_browsingHistory"
            case .shoppingCart: return "slide_shoppingCart"
            case .purchased: return "slide_purchased"
            case .sold: return "slide_sold"
            case .address: return "slide_address"
            case .settings: return "slide_settings"
            case .customerService: return "slide_customerService"
            }
        }
    }
    
    // MARK: - Properties
    weak var delegate: SlideMenuControllerDelegate?
    
    // CustomPresentable协议属性
    var presentationHeight: CGFloat? { return UIScreen.main.bounds.height }
    var presentationDirection: PresentationDirection { return .leftToRight }
    var rightMargin: CGFloat { return 75 }
    
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - UI Components
    private lazy var listView: BaseListView = {
        let view = BaseListView()
        view.delegate = self
        return view
    }()
    
    private lazy var logoutButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("退出登录", for: .normal)
        button.setTitleColor(.systemRed, for: .normal)
        button.backgroundColor = .white
        button.layer.cornerRadius = 22
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.systemRed.cgColor
        button.isHidden = !LoginManager.shared.isLogin()
        button.addTarget(self, action: #selector(logoutButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        configureListItems()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = color_F6F8F9
        fd_prefersNavigationBarHidden = true
        view.addSubview(listView)
        view.addSubview(logoutButton)
        
        listView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top)
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(logoutButton.snp.top).offset(-20)
        }
        
        logoutButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-20)
            make.width.equalToSuperview().offset(-40)
            make.height.equalTo(44)
        }
    }
    
    private func configureListItems() {
        // 定义菜单项分组
        let section1: [ListItemConfig] = [
            makeListItemConfig(for: .addFriend)
        ]
        let section2: [ListItemConfig] = [
            makeListItemConfig(for: .myComments),
            makeListItemConfig(for: .browsingHistory)
        ]
        let section3: [ListItemConfig] = [
            makeListItemConfig(for: .shoppingCart),
            makeListItemConfig(for: .purchased),
            makeListItemConfig(for: .sold),
            makeListItemConfig(for: .address)
        ]
        let section4: [ListItemConfig] = [
            makeListItemConfig(for: .settings)
        ]
        let section5: [ListItemConfig] = [
            makeListItemConfig(for: .customerService)
        ]
        
        // 设置列表项
        listView.setItems([section1, section2, section3, section4, section5])
    }
    
    private func makeListItemConfig(for menuItem: MenuItem) -> ListItemConfig {
        return ListItemConfig(
            type: .select,
            identifier: menuItem.identifier,
            iconString: menuItem.iconString,
            title: menuItem.title
        )
    }
    
    // MARK: - Actions
    @objc private func logoutButtonTapped() {
        dismiss(animated: true) { [weak self] in
            self?.delegate?.slideMenuDidTapLogout()
        }
    }
    
    // MARK: - Public Methods
    static func show(from viewController: UIViewController, delegate: SlideMenuControllerDelegate? = nil) {
        let slideMenuController = SlideMenuController()
        slideMenuController.delegate = delegate
        viewController.customPresent(slideMenuController, animated: true)
    }
}

// MARK: - BaseListViewDelegate
extension SlideMenuController: BaseListViewDelegate {
    func listViewUpdate(_ listView: BaseListView, with data: Any?) {
        // 处理数据更新
    }
    
    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
        // 通过配置的 identifier 查找对应的 MenuItem
        guard let menuItem = MenuItem.allCases.first(where: { $0.identifier == config.identifier }) else {
            return
        }
        
        dismiss(animated: true) { [weak self] in
            self?.delegate?.slideMenuDidSelectItem(menuItem, title: config.title)
        }
    }
    
    func listViewValidate(_ listView: BaseListView, message: String) {
        // 处理验证失败
    }
}
