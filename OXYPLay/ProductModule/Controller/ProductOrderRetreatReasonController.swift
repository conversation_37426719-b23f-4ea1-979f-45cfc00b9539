//
//  ProductOrderRetreatReasonController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/8/2.
//

class ProductOrderRetreatReasonController: BasePresentController {
    
    // MARK: - Properties
    let selectCompletePublisher = PassthroughSubject<Bool, Never>()
    override var presentationHeight: CGFloat? {
        return nil
    }
   
    
    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
    }
    
    // MARK: - UI Setup
    
    override func configUI() {
        configView(title: "选择退货原因", bottomTitle: "确定退货")

        // 添加列表视图
        
    }
   
    
    // MARK: - Bindings
    
    override func setupBindings() {
        super.setupBindings()
        
        bottomButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self  else {
                    return
                }
                self.dismiss(animated: true)
                
            }
            .store(in: &cancellables)
    }
}
