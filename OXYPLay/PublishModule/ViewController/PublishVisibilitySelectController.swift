//
//  PublishVisibilitySelectController.swift
//  OXYPLay
//
//  Created by Renhw on 2025/7/31.
//

import UIKit
import Combine

class PublishVisibilitySelectController: BasePresentController {

    // MARK: - Properties

    var selectListConfig:ListItemConfig?
    let selectCompletedPublisher = PassthroughSubject<(ListItemConfig), Never>()
    let pushBlackWhitePublisher = PassthroughSubject<ListItemConfig, Never>()
    lazy var listView = BaseListView().then {
        $0.delegate = self
    }
    override var presentationHeight: CGFloat? {
        return nil // 返回nil启用自适应高度
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        configUI()
        setupBindings()
        configureListItems()
    }

    // MARK: - UI Setup

    override func configUI() {
        configView(title: "请选择笔记公开情况", bottomTitle: "确定")
        // 添加列表视图
        contentView.addSubview(listView)
        listView.snp.makeConstraints { make in
            make.top.bottom.equalTo(0)
            make.left.right.equalToSuperview()
        }
    }
    ///1公开 2互关好友可见 3仅自己可见 4不然谁看 5谁可以
    private func configureListItems() {
        if let selectListConfig = selectListConfig {
            let items: [[ListItemConfig]] = [
                [ListItemConfig.singleSelect(
                    identifier: "全部可见",
                    title: "全部可见",
                    iconString: "全部可见",
                    isSelected: selectListConfig.data as! String == "1",
                    data: "1"
                ),
                 ListItemConfig.singleSelect(
                     identifier: "仅自己可见",
                     title: "仅自己可见",
                     iconString: "仅自己可见",
                     isSelected: selectListConfig.data as! String == "2",
                     data: "2"
                 ),
                  ListItemConfig.singleSelect(
                      identifier: "仅互关好友可见",
                      title: "仅互关好友可见",
                      iconString: "仅互关好友可见",
                      isSelected:selectListConfig.data as! String == "3",
                      data: "3"
                  ),
                 ListItemConfig.select(
                     identifier: "不给谁看",
                     title: "不给谁看",
                     iconString: "不给谁看",
                     isSelected:selectListConfig.data as! String == "4",
                     subTitle: selectListConfig.data as! String == "4" ? selectListConfig.subTitle:"",
                     data: "4"
                 ),
                 ListItemConfig.select(
                     identifier: "只给谁看",
                     title: "只给谁看",
                     iconString: "只给谁看",
                     isSelected: selectListConfig.data as! String == "5",
                     subTitle: selectListConfig.data as! String == "5" ? selectListConfig.subTitle:"",
                     data: "5"
                 ),
                ]
            ]
            
            // 设置列表项
            listView.setItems(items)
        }
      
    }
    // MARK: - Bindings

    override func setupBindings() {
        super.setupBindings()

        bottomButton.tapPublisher
            .sink { [weak self] _ in
                guard let self = self, let selectListConfig = self.selectListConfig else {
                    // 如果没有选择任何选项，提示用户
                    return
                }

                // 如果选择的是"不给谁看"或"只给谁看"但没有选择用户，提示用户
                if (selectListConfig.identifier == "不给谁看" || selectListConfig.identifier == "只给谁看") &&
                    (selectListConfig.subTitle.isEmpty ?? true) {
                    MBProgressHUD.showPrompt("请先选择用户", in: self.view)
                    return
                }

                self.selectCompletedPublisher.send(selectListConfig)
                self.dismiss(animated: true)
            }
            .store(in: &cancellables)
    }

  
}
// MARK: - BaseListViewDelegate

extension PublishVisibilitySelectController: BaseListViewDelegate {

    func listViewClick(_ listView: BaseListView, config: ListItemConfig) {
        self.selectListConfig = config

        // 更新列表中的选中状态
        configureListItems()

        // 如果选择的是"不给谁看"或"只给谁看"，需要跳转到黑白名单选择页面
        if config.identifier == "不给谁看" || config.identifier == "只给谁看" {
            self.dismiss(animated: true)
            pushBlackWhitePublisher.send(config)
        }
    }
}
